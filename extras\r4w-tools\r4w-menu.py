#!/usr/bin/env python3
# r4w-menu.py: Hauptmenü für r4w-linux Testing Tools
# Version: 1.0.0
# Autor: r4w-linux project
# Verwendung: python3 r4w-menu.py

import os
import sys
import subprocess
import time

class R4WMenu:
    def __init__(self):
        self.tools_dir = "/home/<USER>/extras/r4w-tools"
        self.wordlists_dir = "/usr/share/wordlists"
        
    def banner(self):
        print("""
    ██████╗ ██╗  ██╗██╗    ██╗      ████████╗ ██████╗  ██████╗ ██╗     ███████╗
    ██╔══██╗██║  ██║██║    ██║      ╚══██╔══╝██╔═══██╗██╔═══██╗██║     ██╔════╝
    ██████╔╝███████║██║ █╗ ██║█████╗   ██║   ██║   ██║██║   ██║██║     ███████╗
    ██╔══██╗╚════██║██║███╗██║╚════╝   ██║   ██║   ██║██║   ██║██║     ╚════██║
    ██║  ██║     ██║╚███╔███╔╝         ██║   ╚██████╔╝╚██████╔╝███████╗███████║
    ╚═╝  ╚═╝     ╚═╝ ╚══╝╚══╝          ╚═╝    ╚═════╝  ╚═════╝ ╚══════╝╚══════╝
        """)
        print("    🐍 r4w-linux Testing Suite v1.0")
        print("    🎯 Deine persönliche Testing-Toolbox für Raspberry Pi Zero W")
        print("    ⚠️  Nur für Bildungszwecke und autorisierte Tests!")
        print("")
        
    def show_main_menu(self):
        print("🔧 Verfügbare Tool-Kategorien:")
        print("1. 🕷️  Web Application Security")
        print("2. 🌐 Network Reconnaissance") 
        print("3. 🔐 Password Attacks")
        print("4. 📡 Wireless Security")
        print("5. 🗂️  Wordlist Management")
        print("6. ⚙️  System Tools")
        print("7. 📋 Tool Status & Info")
        print("0. Beenden")
        print("")
        
    def show_web_menu(self):
        print("\n🕷️  Web Application Security Tools:")
        print("1. r4w-scanner - Multi-Purpose Web Vuln Scanner")
        print("2. r4w-xss-checker - Advanced XSS Detection")
        print("3. r4w-payloads - Payload Generator & Encoder")
        print("4. SQLMap - SQL Injection Tool")
        print("5. Nikto - Web Server Scanner")
        print("6. Gobuster - Directory/File Brute Forcer")
        print("7. WPScan - WordPress Security Scanner")
        print("8. Dirb - Web Content Scanner")
        print("0. Zurück zum Hauptmenü")
        print("")
        
    def show_network_menu(self):
        print("\n🌐 Network Reconnaissance Tools:")
        print("1. r4w-recon - Automated Reconnaissance")
        print("2. Nmap - Network Mapper")
        print("3. Masscan - Fast Port Scanner")
        print("4. Netcat - Network Swiss Army Knife")
        print("5. Socat - Advanced Netcat")
        print("6. Chisel - Fast TCP/UDP Tunnel")
        print("7. SSHuttle - VPN over SSH")
        print("0. Zurück zum Hauptmenü")
        print("")
        
    def show_password_menu(self):
        print("\n🔐 Password Attack Tools:")
        print("1. Hydra - Login Brute Forcer")
        print("2. John the Ripper - Password Cracker")
        print("3. Hashcat - Advanced Password Recovery")
        print("4. Medusa - Speedy Brute Forcer")
        print("5. CrackMapExec - SMB/WinRM/LDAP Attacks")
        print("0. Zurück zum Hauptmenü")
        print("")
        
    def show_wireless_menu(self):
        print("\n📡 Wireless Security Tools:")
        print("1. Aircrack-ng Suite - WiFi Security Testing")
        print("2. Wifite - Automated WiFi Attacks")
        print("3. Reaver - WPS PIN Attack")
        print("4. Pixiewps - WPS Pixie Dust Attack")
        print("5. Macchanger - MAC Address Changer")
        print("0. Zurück zum Hauptmenü")
        print("")
        
    def show_wordlist_menu(self):
        print("\n🗂️  Wordlist Management:")
        print("1. Zeige verfügbare Wordlists")
        print("2. Download rockyou.txt")
        print("3. Download SecLists")
        print("4. Download FuzzDB")
        print("5. Custom Wordlist erstellen")
        print("6. Wordlist Statistiken")
        print("0. Zurück zum Hauptmenü")
        print("")
        
    def show_system_menu(self):
        print("\n⚙️  System Tools:")
        print("1. Metasploit Framework")
        print("2. Tor Browser (CLI)")
        print("3. ProxyChains")
        print("4. Wireshark/TShark")
        print("5. System Update")
        print("6. Tool Installation")
        print("0. Zurück zum Hauptmenü")
        print("")
        
    def run_tool(self, command, description=""):
        print(f"\n🚀 Starte: {description}")
        print(f"💻 Befehl: {command}")
        print("=" * 50)
        
        try:
            if command.startswith("python3"):
                # Für Python-Scripts im tools_dir
                script_name = command.split()[-1]
                script_path = os.path.join(self.tools_dir, script_name)
                if os.path.exists(script_path):
                    subprocess.run([sys.executable, script_path])
                else:
                    print(f"❌ Script nicht gefunden: {script_path}")
            else:
                # Für System-Commands
                subprocess.run(command, shell=True)
        except KeyboardInterrupt:
            print("\n\n⏹️  Tool beendet!")
        except Exception as e:
            print(f"\n❌ Fehler beim Ausführen: {e}")
            
        input("\n📋 Drücke Enter um fortzufahren...")
        
    def handle_web_tools(self, choice):
        if choice == '1':
            self.run_tool("python3 r4w-scanner.py", "r4w Multi-Purpose Scanner")
        elif choice == '2':
            self.run_tool("python3 r4w-xss-checker.py", "r4w XSS Checker")
        elif choice == '3':
            self.run_tool("python3 r4w-payloads.py", "r4w Payload Generator")
        elif choice == '4':
            target = input("🎯 Ziel-URL: ")
            self.run_tool(f"sqlmap -u {target} --batch", "SQLMap")
        elif choice == '5':
            target = input("🎯 Ziel-URL: ")
            self.run_tool(f"nikto -h {target}", "Nikto Web Scanner")
        elif choice == '6':
            target = input("🎯 Ziel-URL: ")
            wordlist = input("📝 Wordlist (Enter für Standard): ") or "/usr/share/wordlists/dirb/common.txt"
            self.run_tool(f"gobuster dir -u {target} -w {wordlist}", "Gobuster")
        elif choice == '7':
            target = input("🎯 WordPress URL: ")
            self.run_tool(f"wpscan --url {target}", "WPScan")
        elif choice == '8':
            target = input("🎯 Ziel-URL: ")
            self.run_tool(f"dirb {target}", "Dirb")
            
    def handle_network_tools(self, choice):
        if choice == '1':
            self.run_tool("python3 r4w-recon.py", "r4w Automated Reconnaissance")
        elif choice == '2':
            target = input("🎯 Ziel (IP/Domain): ")
            self.run_tool(f"nmap -sS -sV {target}", "Nmap Scan")
        elif choice == '3':
            target = input("🎯 Ziel (IP/CIDR): ")
            ports = input("🔌 Ports (Enter für 1-1000): ") or "1-1000"
            self.run_tool(f"masscan {target} -p{ports} --rate=1000", "Masscan")
        elif choice == '4':
            print("💡 Netcat Beispiele:")
            print("   Listener: nc -lvp 4444")
            print("   Connect: nc <target> <port>")
            command = input("💻 Netcat Befehl: ")
            self.run_tool(f"nc {command}", "Netcat")
        elif choice == '5':
            print("💡 Socat Beispiele:")
            print("   TCP Relay: socat TCP-LISTEN:8080,fork TCP:target:80")
            command = input("💻 Socat Befehl: ")
            self.run_tool(f"socat {command}", "Socat")
        elif choice == '6':
            print("💡 Chisel Tunnel Setup")
            mode = input("🔧 Server (s) oder Client (c): ")
            if mode.lower() == 's':
                port = input("🔌 Server Port (8080): ") or "8080"
                self.run_tool(f"chisel server -p {port} --reverse", "Chisel Server")
            else:
                server = input("🎯 Server IP:Port: ")
                tunnel = input("🔗 Tunnel Config (R:8000:localhost:8000): ") or "R:8000:localhost:8000"
                self.run_tool(f"chisel client {server} {tunnel}", "Chisel Client")
        elif choice == '7':
            target = input("🎯 SSH Target (user@host): ")
            subnet = input("🌐 Subnet (***********/24): ")
            self.run_tool(f"sshuttle -r {target} {subnet}", "SSHuttle VPN")
            
    def handle_password_tools(self, choice):
        if choice == '1':
            service = input("🎯 Service (ssh/ftp/http-get): ")
            target = input("🎯 Target (IP:Port): ")
            userlist = input("👤 Userlist: ") or "/usr/share/wordlists/metasploit/unix_users.txt"
            passlist = input("🔐 Passlist: ") or "/usr/share/wordlists/rockyou.txt"
            self.run_tool(f"hydra -L {userlist} -P {passlist} {target} {service}", "Hydra")
        elif choice == '2':
            hashfile = input("🔐 Hash File: ")
            wordlist = input("📝 Wordlist: ") or "/usr/share/wordlists/rockyou.txt"
            self.run_tool(f"john --wordlist={wordlist} {hashfile}", "John the Ripper")
        elif choice == '3':
            hashfile = input("🔐 Hash File: ")
            wordlist = input("📝 Wordlist: ") or "/usr/share/wordlists/rockyou.txt"
            hashtype = input("🔢 Hash Type (0=MD5, 1000=NTLM): ") or "0"
            self.run_tool(f"hashcat -m {hashtype} {hashfile} {wordlist}", "Hashcat")
        elif choice == '4':
            service = input("🎯 Service (ssh/ftp/telnet): ")
            target = input("🎯 Target: ")
            userlist = input("👤 Userlist: ")
            passlist = input("🔐 Passlist: ")
            self.run_tool(f"medusa -h {target} -U {userlist} -P {passlist} -M {service}", "Medusa")
        elif choice == '5':
            target = input("🎯 Target IP/Range: ")
            username = input("👤 Username: ")
            password = input("🔐 Password: ")
            self.run_tool(f"crackmapexec smb {target} -u {username} -p {password}", "CrackMapExec")
            
    def handle_wireless_tools(self, choice):
        if choice == '1':
            print("💡 Aircrack-ng Suite:")
            print("   airmon-ng - Monitor Mode")
            print("   airodump-ng - Packet Capture")
            print("   aireplay-ng - Packet Injection")
            print("   aircrack-ng - WEP/WPA Cracking")
            tool = input("🔧 Tool auswählen: ")
            params = input("⚙️  Parameter: ")
            self.run_tool(f"{tool} {params}", f"Aircrack-ng: {tool}")
        elif choice == '2':
            interface = input("📡 WLAN Interface (wlan0): ") or "wlan0"
            self.run_tool(f"wifite -i {interface}", "Wifite")
        elif choice == '3':
            interface = input("📡 WLAN Interface: ")
            bssid = input("🎯 Target BSSID: ")
            self.run_tool(f"reaver -i {interface} -b {bssid} -vv", "Reaver")
        elif choice == '4':
            self.run_tool("pixiewps --help", "Pixiewps Help")
        elif choice == '5':
            interface = input("📡 Interface: ")
            new_mac = input("🔧 Neue MAC (random für zufällig): ")
            if new_mac.lower() == "random":
                self.run_tool(f"macchanger -r {interface}", "MAC Changer (Random)")
            else:
                self.run_tool(f"macchanger -m {new_mac} {interface}", "MAC Changer")
                
    def handle_wordlist_management(self, choice):
        if choice == '1':
            self.run_tool(f"find {self.wordlists_dir} -name '*.txt' | head -20", "Verfügbare Wordlists")
        elif choice == '2':
            print("📥 Downloading rockyou.txt...")
            self.run_tool("wget -O /tmp/rockyou.txt.gz https://github.com/brannondorsey/naive-hashcat/releases/download/data/rockyou.txt && gunzip /tmp/rockyou.txt.gz && sudo mv /tmp/rockyou.txt /usr/share/wordlists/", "Download rockyou.txt")
        elif choice == '3':
            print("📥 Downloading SecLists...")
            self.run_tool("cd /tmp && git clone https://github.com/danielmiessler/SecLists.git && sudo mv SecLists /usr/share/wordlists/", "Download SecLists")
        elif choice == '4':
            print("📥 Downloading FuzzDB...")
            self.run_tool("cd /tmp && git clone https://github.com/fuzzdb-project/fuzzdb.git && sudo mv fuzzdb /usr/share/wordlists/", "Download FuzzDB")
        elif choice == '5':
            filename = input("📝 Wordlist Name: ")
            print("💡 Gib Wörter ein (Ctrl+D zum Beenden):")
            words = []
            try:
                while True:
                    word = input()
                    words.append(word)
            except EOFError:
                pass
            with open(f"/tmp/{filename}", 'w') as f:
                f.write('\n'.join(words))
            print(f"✅ Wordlist erstellt: /tmp/{filename}")
        elif choice == '6':
            wordlist = input("📝 Wordlist Pfad: ")
            self.run_tool(f"wc -l {wordlist} && head -5 {wordlist}", "Wordlist Stats")
            
    def handle_system_tools(self, choice):
        if choice == '1':
            print("🚀 Starte Metasploit Framework...")
            self.run_tool("msfconsole", "Metasploit Framework")
        elif choice == '2':
            url = input("🌐 URL (Enter für Tor Check): ") or "https://check.torproject.org"
            self.run_tool(f"torsocks curl -s {url}", "Tor Browser (CLI)")
        elif choice == '3':
            command = input("💻 Command mit ProxyChains: ")
            self.run_tool(f"proxychains4 {command}", "ProxyChains")
        elif choice == '4':
            mode = input("🔧 Wireshark (w) oder TShark (t): ")
            if mode.lower() == 'w':
                self.run_tool("wireshark", "Wireshark GUI")
            else:
                interface = input("📡 Interface (eth0/wlan0): ")
                self.run_tool(f"tshark -i {interface}", "TShark")
        elif choice == '5':
            print("🔄 System Update...")
            self.run_tool("sudo apt update && sudo apt upgrade -y", "System Update")
        elif choice == '6':
            tool = input("🔧 Tool zum Installieren: ")
            self.run_tool(f"sudo apt install -y {tool}", f"Install {tool}")
            
    def show_tool_status(self):
        print("\n📋 Tool Status & Information:")
        print("=" * 40)
        
        tools = [
            ("nmap", "Network Mapper"),
            ("sqlmap", "SQL Injection Tool"),
            ("hydra", "Login Brute Forcer"),
            ("john", "Password Cracker"),
            ("aircrack-ng", "WiFi Security Suite"),
            ("metasploit", "Exploitation Framework"),
            ("tor", "Anonymity Network"),
            ("wireshark", "Network Protocol Analyzer")
        ]
        
        for tool, description in tools:
            try:
                result = subprocess.run(f"which {tool}", shell=True, capture_output=True)
                status = "✅ Installiert" if result.returncode == 0 else "❌ Nicht gefunden"
                print(f"{tool:15} - {description:25} [{status}]")
            except:
                print(f"{tool:15} - {description:25} [❌ Fehler]")
                
        print(f"\n📁 Wordlists Verzeichnis: {self.wordlists_dir}")
        print(f"📁 r4w-Tools Verzeichnis: {self.tools_dir}")
        
        input("\n📋 Drücke Enter um fortzufahren...")
        
    def run(self):
        while True:
            os.system('clear' if os.name == 'posix' else 'cls')
            self.banner()
            self.show_main_menu()
            
            choice = input("Auswahl: ").strip()
            
            if choice == '0':
                print("👋 Auf Wiedersehen! Happy Testing! 🐍")
                break
            elif choice == '1':
                while True:
                    os.system('clear' if os.name == 'posix' else 'cls')
                    self.banner()
                    self.show_web_menu()
                    web_choice = input("Auswahl: ").strip()
                    if web_choice == '0':
                        break
                    self.handle_web_tools(web_choice)
            elif choice == '2':
                while True:
                    os.system('clear' if os.name == 'posix' else 'cls')
                    self.banner()
                    self.show_network_menu()
                    net_choice = input("Auswahl: ").strip()
                    if net_choice == '0':
                        break
                    self.handle_network_tools(net_choice)
            elif choice == '3':
                while True:
                    os.system('clear' if os.name == 'posix' else 'cls')
                    self.banner()
                    self.show_password_menu()
                    pass_choice = input("Auswahl: ").strip()
                    if pass_choice == '0':
                        break
                    self.handle_password_tools(pass_choice)
            elif choice == '4':
                while True:
                    os.system('clear' if os.name == 'posix' else 'cls')
                    self.banner()
                    self.show_wireless_menu()
                    wifi_choice = input("Auswahl: ").strip()
                    if wifi_choice == '0':
                        break
                    self.handle_wireless_tools(wifi_choice)
            elif choice == '5':
                while True:
                    os.system('clear' if os.name == 'posix' else 'cls')
                    self.banner()
                    self.show_wordlist_menu()
                    wl_choice = input("Auswahl: ").strip()
                    if wl_choice == '0':
                        break
                    self.handle_wordlist_management(wl_choice)
            elif choice == '6':
                while True:
                    os.system('clear' if os.name == 'posix' else 'cls')
                    self.banner()
                    self.show_system_menu()
                    sys_choice = input("Auswahl: ").strip()
                    if sys_choice == '0':
                        break
                    self.handle_system_tools(sys_choice)
            elif choice == '7':
                self.show_tool_status()
            else:
                print("❌ Ungültige Auswahl!")
                time.sleep(1)

if __name__ == "__main__":
    menu = R4WMenu()
    try:
        menu.run()
    except KeyboardInterrupt:
        print("\n\n👋 r4w-menu beendet!")
    except Exception as e:
        print(f"\n❌ Fehler: {e}")

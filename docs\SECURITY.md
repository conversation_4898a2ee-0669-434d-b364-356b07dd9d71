# 🔐 r4w-linux Sicherheit

## ⚠️ Wichtige Sicherheitshinweise

**r4w-linux ist ausschließlich für Bildungszwecke bestimmt!**

### Sofort nach Installation durchführen

#### 1. Passwort ändern
```bash
passwd
```

#### 2. SSH-Key ersetzen
Der mitgelieferte Dummy-Key ist **NICHT SICHER**:

```bash
# Neuen Key generieren
ssh-keygen -t rsa -b 4096 -f ~/.ssh/id_r4w_secure

# Public Key auf Pi kopieren
scp ~/.ssh/id_r4w_secure.pub kali@192.168.1.42:/home/<USER>/.ssh/authorized_keys

# SSH-Config aktualisieren
sed -i 's/id_r4w/id_r4w_secure/' ~/.ssh/config
```

#### 3. System aktualisieren
```bash
sudo apt update && sudo apt upgrade -y
```

## Sicherheitskonfiguration

### SSH-Härtung

SSH ist bereits vorkonfiguriert mit:
- Passwort-Login deaktiviert
- Nur Key-basierte Authentifizierung
- Root-Login deaktiviert

Zusätzliche Härtung:
```bash
sudo nano /etc/ssh/sshd_config
```

Empfohlene Einstellungen:
```conf
Port 2222                    # Standard-Port ändern
PermitRootLogin no
PasswordAuthentication no
PubkeyAuthentication yes
AuthorizedKeysFile .ssh/authorized_keys
MaxAuthTries 3
ClientAliveInterval 300
ClientAliveCountMax 2
```

### Firewall konfigurieren

UFW aktivieren:
```bash
sudo ufw enable
sudo ufw default deny incoming
sudo ufw default allow outgoing
sudo ufw allow 22/tcp          # oder dein SSH-Port
sudo ufw allow 80/tcp          # falls Webserver benötigt
sudo ufw allow 443/tcp         # falls HTTPS benötigt
```

### Fail2Ban installieren

Schutz vor Brute-Force-Angriffen:
```bash
sudo apt install fail2ban -y
sudo systemctl enable fail2ban
sudo systemctl start fail2ban
```

Konfiguration:
```bash
sudo nano /etc/fail2ban/jail.local
```

```ini
[DEFAULT]
bantime = 3600
findtime = 600
maxretry = 3

[sshd]
enabled = true
port = ssh
filter = sshd
logpath = /var/log/auth.log
maxretry = 3
```

### Automatische Updates

Unattended-Upgrades aktivieren:
```bash
sudo apt install unattended-upgrades -y
sudo dpkg-reconfigure -plow unattended-upgrades
```

### Monitoring

#### Logwatch installieren
```bash
sudo apt install logwatch -y
```

#### Intrusion Detection
```bash
sudo apt install aide -y
sudo aideinit
sudo mv /var/lib/aide/aide.db.new /var/lib/aide/aide.db
```

## Netzwerk-Sicherheit

### VPN-Verbindung

WireGuard für sichere Verbindungen:
```bash
sudo apt install wireguard -y
```

### SSH-Tunnel

Für sichere Verbindungen über unsichere Netzwerke:
```bash
ssh -D 8080 -C -N hack
```

### MAC-Adresse randomisieren

```bash
sudo nano /etc/systemd/network/99-default.link
```

```ini
[Match]
OriginalName=wlan0

[Link]
MACAddressPolicy=random
```

## Incident Response

### Logs überwachen

Wichtige Log-Dateien:
- `/var/log/auth.log` - SSH-Logins
- `/var/log/syslog` - System-Events
- `/var/log/r4w-autostart.log` - r4w-spezifische Logs

### Notfall-Prozeduren

#### SSH-Zugang verloren
1. SD-Karte in PC einlegen
2. `boot`-Partition mounten
3. SSH-Key in `authorized_keys` korrigieren

#### Kompromittierung vermutet
1. Netzwerk trennen
2. Logs sichern
3. System neu aufsetzen

## Best Practices

### Regelmäßige Wartung
- Wöchentliche Updates
- Log-Review
- Key-Rotation (monatlich)
- Backup der Konfiguration

### Sichere Nutzung
- Nur in isolierten Netzwerken testen
- Keine produktiven Systeme angreifen
- Immer Erlaubnis einholen
- Logs und Aktivitäten dokumentieren

### Rechtliche Hinweise
- Nur auf eigenen Systemen verwenden
- Penetration Testing nur mit schriftlicher Erlaubnis
- Lokale Gesetze beachten
- Ethische Testing-Prinzipien befolgen

## Weitere Ressourcen

- [OWASP Testing Guide](https://owasp.org/www-project-web-security-testing-guide/)
- [NIST Cybersecurity Framework](https://www.nist.gov/cyberframework)
- [Kali Linux Documentation](https://www.kali.org/docs/)

# 💾 Speicherplatz-Optimierung für r4w-linux

## Problem
Das ursprüngliche r4w-linux Image war ~10.8GB groß und passte nicht auf 8GB USB-Sticks.

## Lösung: Minimal Build
Neue **r4w-linux-minimal** Version mit <8GB Größe für Standard USB-Sticks.

---

## 🔧 Optimierungen implementiert

### 1. Basis-Image Reduktion
```bash
# Entfernte Pakete aus Base-Image:
- firefox-esr          # ~300MB
- metasploit-framework  # ~1.5GB
- hashcat              # ~200MB
- wireshark-common     # ~150MB
- playwright + chromium # ~500MB
- aircrack-ng suite    # ~100MB
- john the ripper      # ~50MB
- SecLists wordlists   # ~300MB
- rockyou.txt          # ~140MB
```

### 2. Hardware-Optimierungen
```bash
# GPU Memory reduziert (headless)
gpu_mem=16              # Standard: 64MB → 16MB

# Swap-File verkleinert
CONF_SWAPSIZE=100       # Standard: 512MB → 100MB
```

### 3. System-Cleanup
```bash
# Entfernte GUI-Komponenten:
- libreoffice*
- thunderbird*
- games-*
- sound-*
- pulseaudio*
- bluetooth*
- cups* (Drucker)
```

---

## 📦 Zwei Build-Varianten

### 🔥 Vollversion (r4w-linux.img.xz)
- **Größe:** ~10GB
- **Zielgruppe:** 16GB+ USB-Sticks
- **Vorteil:** Sofort einsatzbereit
- **Build:** `make build` oder `build-r4w.sh`

### 💾 Minimal (r4w-linux-minimal.img.xz)
- **Größe:** <3GB
- **Zielgruppe:** 8GB USB-Sticks
- **Vorteil:** Passt auf Standard 8gb Sticks
- **Build:** `make build-minimal` oder `build-r4w-minimal.sh`
- **Setup:** Post-Boot Installation erforderlich

---

## 🚀 Post-Boot Setup

Für die Minimal-Version:

```bash
# Nach dem ersten Login ausführen:
sudo /home/<USER>/extras/post-boot-setup.sh
```

### Was wird nachinstalliert:
- Metasploit Framework
- Firefox ESR
- Hashcat & John the Ripper
- Wireshark & Aircrack-ng
- Playwright + Chromium
- Wordlists (rockyou.txt, SecLists)
- Alle anderen Testing-Tools

### Installationszeit:
- **Pi Zero W:** ~45-60 Minuten
- **Pi 4:** ~15-20 Minuten

---

## 📊 Speicherplatz-Vergleich

| Komponente | Vollversion | Minimal | Gespart |
|------------|-------------|---------|---------|
| Base Image | 10.8GB | 7.2GB | 3.6GB |
| Nach Komprimierung | 4.2GB | 2.8GB | 1.4GB |
| Auf USB-Stick | 10.8GB | 7.2GB | 3.6GB |

---

## 🎯 Empfehlungen

### Für 8GB USB-Sticks:
```bash
# Verwende Minimal Build
make build-minimal
```

### Für 16GB+ USB-Sticks:
```bash
# Verwende Vollversion
make build
```

### Für Entwicklung/Testing:
```bash
# Minimal für schnellere Builds
make build-minimal
```

---

## 🔧 Weitere Optimierungen (optional)

### Zusätzliche Speicherplatz-Einsparungen:
```bash
# Entferne Dokumentation (nach Installation)
sudo rm -rf /usr/share/doc/*
sudo rm -rf /usr/share/man/*

# Entferne Locale-Dateien (außer Deutsch)
sudo localepurge

# Entferne APT-Cache
sudo apt-get clean
sudo apt-get autoclean
```

### Performance-Optimierungen für Pi Zero W:
```bash
# Reduziere Journald-Logs
echo "SystemMaxUse=50M" >> /etc/systemd/journald.conf

# Deaktiviere unnötige Services
sudo systemctl disable bluetooth
sudo systemctl disable cups
sudo systemctl disable avahi-daemon
```

---

## 🚨 Wichtige Hinweise

### Minimal-Version Limitierungen:
- ❌ Metasploit nicht sofort verfügbar
- ❌ Wordlists müssen nachgeladen werden
- ❌ Browser-Tools erst nach Setup
- ✅ Basis-r4w-Tools funktionieren sofort

### Internet-Verbindung erforderlich:
Das Post-Boot Setup benötigt eine aktive Internetverbindung für:
- Paket-Downloads (~2GB)
- Wordlist-Downloads (~500MB)
- Browser-Installation

### Fallback-Strategie:
Falls Post-Boot Setup fehlschlägt:
```bash
# Manuelle Installation einzelner Tools
sudo apt update
sudo apt install metasploit-framework
sudo apt install firefox-esr
# etc.
```

---

## 📝 Build-Scripts

### Linux/WSL:
- `build-r4w.sh` - Vollversion
- `build-r4w-minimal.sh` - Minimal

### Windows PowerShell:
- `scripts/build-r4w-native-windows.ps1` - Vollversion
- `scripts/build-r4w-minimal.ps1` - Minimal

### Makefile-Targets:
- `make build` - Vollversion
- `make build-minimal` - Minimal
- `make clean` - Aufräumen
